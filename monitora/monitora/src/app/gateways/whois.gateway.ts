import { Provider } from "@core/decorators/decorators";
import { Mock } from "@core/decorators/mock.decorator";
// eslint-disable-next-line @typescript-eslint/no-var-requires
const whois = require("whois");

@Provider()
export class WhoisGateway {
  @Mock("WhoisGateway-lookup")
  async lookup(domain: string): Promise<any> {
    try {
      return new Promise((resolve, reject) => {
        whois.lookup(domain, (err: any, data: string) => {
          if (err) {
            reject(err);
            return;
          }

          // Parse WHOIS data
          const parsed = this.parseWhoisData(data);
          resolve(parsed);
        });
      });
    } catch (error) {
      console.error(`Error looking up WHOIS for ${domain}:`, error);
      return null;
    }
  }

  private parseWhoisData(data: string): any {
    const lines = data.split("\n");
    const result: any = {
      registrar: null,
      registrationDate: null,
      expirationDate: null,
      registrantName: null,
      registrantEmail: null,
      registrantCountry: null,
      nameServers: [],
      raw: data,
    };

    lines.forEach((line) => {
      const cleanLine = line.trim().toLowerCase();

      // Registrar
      if (
        cleanLine.includes("registrar:") ||
        cleanLine.includes("registrar name:")
      ) {
        result.registrar = this.extractValue(line);
      }

      // Registration date
      if (
        cleanLine.includes("creation date:") ||
        cleanLine.includes("created:") ||
        cleanLine.includes("registered:")
      ) {
        const dateStr = this.extractValue(line);
        result.registrationDate = this.parseDate(dateStr);
      }

      // Expiration date
      if (
        cleanLine.includes("expiry date:") ||
        cleanLine.includes("expires:") ||
        cleanLine.includes("expiration:")
      ) {
        const dateStr = this.extractValue(line);
        result.expirationDate = this.parseDate(dateStr);
      }

      // Registrant name
      if (
        cleanLine.includes("registrant name:") ||
        cleanLine.includes("registrant:")
      ) {
        result.registrantName = this.extractValue(line);
      }

      // Registrant email
      if (
        cleanLine.includes("registrant email:") ||
        cleanLine.includes("admin email:")
      ) {
        result.registrantEmail = this.extractValue(line);
      }

      // Registrant country
      if (
        cleanLine.includes("registrant country:") ||
        cleanLine.includes("country:")
      ) {
        result.registrantCountry = this.extractValue(line);
      }

      // Name servers
      if (
        cleanLine.includes("name server:") ||
        cleanLine.includes("nserver:")
      ) {
        const nameServer = this.extractValue(line);
        if (nameServer && !result.nameServers.includes(nameServer)) {
          result.nameServers.push(nameServer);
        }
      }
    });

    return result;
  }

  private extractValue(line: string): string | null {
    const colonIndex = line.indexOf(":");
    if (colonIndex === -1) return null;

    return line.substring(colonIndex + 1).trim() || null;
  }

  private parseDate(dateStr: string | null): Date | null {
    if (!dateStr) return null;

    try {
      // Remove timezone info and extra text
      const cleanDateStr = dateStr.split("T")[0].split(" ")[0];
      const date = new Date(cleanDateStr);
      return isNaN(date.getTime()) ? null : date;
    } catch {
      return null;
    }
  }
}
