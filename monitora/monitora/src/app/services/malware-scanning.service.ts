import { Provider } from "@core/decorators/decorators";
import { Mo<PERSON> } from "@core/decorators/mock.decorator";
import axios from "axios";

interface VirusTotalResponse {
  data: {
    id: string;
    type: string;
    attributes: {
      stats: {
        harmless: number;
        malicious: number;
        suspicious: number;
        undetected: number;
        timeout: number;
      };
      last_analysis_results: { [key: string]: any };
      last_analysis_date: number;
      reputation: number;
    };
  };
}

interface MalwareScanResult {
  isMalicious: boolean;
  threats: string[];
  scanDate: Date;
  confidence: number;
  details: {
    engines: number;
    detections: number;
    reputation: number;
    categories: string[];
  };
  rawResponse?: any;
}

@Provider()
export class MalwareScanningService {
  private virusTotalApiKey: string;
  private virusTotalBaseUrl = "https://www.virustotal.com/api/v3";

  constructor() {
    this.virusTotalApiKey = process.env.VIRUSTOTAL_API_KEY || "";
    if (!this.virusTotalApiKey) {
      console.warn(
        "VirusTotal API key not configured. Malware scanning will be limited."
      );
    }
  }

  @Mock("MalwareScanningService-scanUrl")
  async scanUrl(url: string): Promise<MalwareScanResult> {
    if (!this.virusTotalApiKey) {
      return this.getMockScanResult(url);
    }

    try {
      // Submit URL for scanning
      const submitResponse = await axios.post(
        `${this.virusTotalBaseUrl}/urls`,
        `url=${encodeURIComponent(url)}`,
        {
          headers: {
            "x-apikey": this.virusTotalApiKey,
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      const analysisId = submitResponse.data.data.id;

      // Wait a bit for analysis to complete
      await this.delay(5000);

      // Get analysis results
      const resultResponse = await axios.get(
        `${this.virusTotalBaseUrl}/analyses/${analysisId}`,
        {
          headers: {
            "x-apikey": this.virusTotalApiKey,
          },
        }
      );

      return this.parseVirusTotalResponse(resultResponse.data, url);
    } catch (error) {
      console.error(`Error scanning URL ${url} with VirusTotal:`, error);

      // Fallback to basic checks
      return this.performBasicMalwareCheck(url);
    }
  }

  @Mock("MalwareScanningService-scanDomain")
  async scanDomain(domain: string): Promise<MalwareScanResult> {
    if (!this.virusTotalApiKey) {
      return this.getMockScanResult(domain);
    }

    try {
      const response = await axios.get(
        `${this.virusTotalBaseUrl}/domains/${domain}`,
        {
          headers: {
            "x-apikey": this.virusTotalApiKey,
          },
        }
      );

      return this.parseVirusTotalResponse(response.data, domain);
    } catch (error) {
      console.error(`Error scanning domain ${domain} with VirusTotal:`, error);
      return this.performBasicMalwareCheck(domain);
    }
  }

  @Mock("MalwareScanningService-batchScan")
  async batchScan(urls: string[]): Promise<
    Array<{
      url: string;
      result: MalwareScanResult;
      success: boolean;
      error?: string;
    }>
  > {
    const results = [];

    for (const url of urls) {
      try {
        const result = await this.scanUrl(url);
        results.push({
          url,
          result,
          success: true,
        });

        // Rate limiting - VirusTotal has API limits
        await this.delay(1000);
      } catch (error) {
        results.push({
          url,
          result: this.getMockScanResult(url),
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return results;
  }

  private parseVirusTotalResponse(
    response: VirusTotalResponse,
    target: string
  ): MalwareScanResult {
    const stats = response.data.attributes.stats;
    const totalEngines =
      stats.harmless + stats.malicious + stats.suspicious + stats.undetected;
    const detections = stats.malicious + stats.suspicious;

    const threats: string[] = [];
    const categories: string[] = [];

    // Extract threat information from analysis results
    if (response.data.attributes.last_analysis_results) {
      Object.entries(response.data.attributes.last_analysis_results).forEach(
        ([engine, result]: [string, any]) => {
          if (
            result.category === "malicious" ||
            result.category === "suspicious"
          ) {
            if (result.result && result.result !== "Clean") {
              threats.push(`${engine}: ${result.result}`);
            }
            if (result.category && !categories.includes(result.category)) {
              categories.push(result.category);
            }
          }
        }
      );
    }

    const isMalicious = detections > 0;
    const confidence = totalEngines > 0 ? (detections / totalEngines) * 100 : 0;

    return {
      isMalicious,
      threats,
      scanDate: new Date(),
      confidence,
      details: {
        engines: totalEngines,
        detections,
        reputation: response.data.attributes.reputation || 0,
        categories,
      },
      rawResponse: response.data,
    };
  }

  private async performBasicMalwareCheck(
    target: string
  ): Promise<MalwareScanResult> {
    // Basic heuristic checks when VirusTotal is not available
    const suspiciousPatterns = [
      /\.tk$/i,
      /\.ml$/i,
      /\.ga$/i,
      /\.cf$/i,
      /phishing/i,
      /malware/i,
      /virus/i,
      /trojan/i,
      /scam/i,
      /fake/i,
    ];

    const threats = [];
    let isMalicious = false;

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(target)) {
        threats.push(`Suspicious pattern detected: ${pattern.source}`);
        isMalicious = true;
      }
    }

    return {
      isMalicious,
      threats,
      scanDate: new Date(),
      confidence: isMalicious ? 30 : 0, // Low confidence for basic checks
      details: {
        engines: 1,
        detections: threats.length,
        reputation: 0,
        categories: isMalicious ? ["suspicious"] : [],
      },
    };
  }

  private getMockScanResult(target: string): MalwareScanResult {
    // Mock result for testing/development
    return {
      isMalicious: false,
      threats: [],
      scanDate: new Date(),
      confidence: 0,
      details: {
        engines: 0,
        detections: 0,
        reputation: 0,
        categories: [],
      },
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  @Mock("MalwareScanningService-getApiStatus")
  async getApiStatus(): Promise<{
    available: boolean;
    quotaRemaining?: number;
    quotaLimit?: number;
  }> {
    if (!this.virusTotalApiKey) {
      return { available: false };
    }

    try {
      const response = await axios.get(
        `${this.virusTotalBaseUrl}/users/${this.virusTotalApiKey.substring(
          0,
          8
        )}`,
        {
          headers: {
            "x-apikey": this.virusTotalApiKey,
          },
        }
      );

      return {
        available: true,
        quotaRemaining: response.headers["x-api-quota-remaining"]
          ? parseInt(response.headers["x-api-quota-remaining"])
          : undefined,
        quotaLimit: response.headers["x-api-quota-limit"]
          ? parseInt(response.headers["x-api-quota-limit"])
          : undefined,
      };
    } catch (error) {
      console.error("Error checking VirusTotal API status:", error);
      return { available: false };
    }
  }
}
