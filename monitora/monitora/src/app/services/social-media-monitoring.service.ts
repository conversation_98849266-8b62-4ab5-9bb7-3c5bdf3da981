import { Provider } from "@core/decorators/decorators";
import { SocialMediaMonitorRepository } from "../repositories/social-media-monitor.repository";
import { ThreatDetectionService } from "./threat-detection.service";
import { MetaGateway } from "../gateways/meta.gateway";
import { TwitterGateway } from "../gateways/twitter.gateway";
import { GoogleGateway } from "../gateways/google.gateway";
import { <PERSON><PERSON> } from "@core/decorators/mock.decorator";
import { SocialMediaMonitor } from "../../entities/social-media-monitor.entity";
import { Brand } from "../../entities/brand.entity";
import similarity from "similarity";

@Provider()
export class SocialMediaMonitoringService {
  constructor(
    private socialMediaMonitorRepository: SocialMediaMonitorRepository,
    private threatDetectionService: ThreatDetectionService,
    private metaGateway: MetaGateway,
    private twitterGateway: TwitterGateway,
    private googleGateway: GoogleGateway
  ) {}

  @Mock("SocialMediaMonitoringService-scanSocialMedia")
  async scanSocialMedia(
    brand: Brand,
    platforms?: string[]
  ): Promise<SocialMediaMonitor[]> {
    const suspiciousContent: SocialMediaMonitor[] = [];
    const platformsToScan = platforms || [
      "facebook",
      "instagram",
      "twitter",
      "youtube",
    ];

    if (!brand.keywords || brand.keywords.length === 0) {
      return suspiciousContent;
    }

    for (const platform of platformsToScan) {
      try {
        console.log(`Scanning ${platform} for brand: ${brand.name}`);

        switch (platform) {
          case "facebook":
            const facebookContent = await this.scanFacebook(brand);
            suspiciousContent.push(...facebookContent);
            break;

          case "instagram":
            const instagramContent = await this.scanInstagram(brand);
            suspiciousContent.push(...instagramContent);
            break;

          case "twitter":
            const twitterContent = await this.scanTwitter(brand);
            suspiciousContent.push(...twitterContent);
            break;

          case "youtube":
            const youtubeContent = await this.scanYouTube(brand);
            suspiciousContent.push(...youtubeContent);
            break;
        }
      } catch (error) {
        console.error(
          `Error scanning ${platform} for brand ${brand.name}:`,
          error
        );
      }
    }

    return suspiciousContent;
  }

  private async scanFacebook(brand: Brand): Promise<SocialMediaMonitor[]> {
    const results: SocialMediaMonitor[] = [];

    for (const keyword of brand.keywords) {
      try {
        // Search for pages
        const pages = await this.metaGateway.searchPages(keyword);
        for (const page of pages) {
          const monitor = await this.analyzeFacebookPage(page, brand, keyword);
          if (monitor) {
            results.push(monitor);
          }
        }

        // Search for posts
        const posts = await this.metaGateway.searchPosts(keyword);
        for (const post of posts) {
          const monitor = await this.analyzeFacebookPost(post, brand, keyword);
          if (monitor) {
            results.push(monitor);
          }
        }
      } catch (error) {
        console.error(`Error scanning Facebook for keyword ${keyword}:`, error);
      }
    }

    return results;
  }

  private async scanInstagram(brand: Brand): Promise<SocialMediaMonitor[]> {
    const results: SocialMediaMonitor[] = [];

    for (const keyword of brand.keywords) {
      try {
        const posts = await this.metaGateway.getInstagramPosts(keyword);
        for (const post of posts) {
          const monitor = await this.analyzeInstagramPost(post, brand, keyword);
          if (monitor) {
            results.push(monitor);
          }
        }
      } catch (error) {
        console.error(
          `Error scanning Instagram for keyword ${keyword}:`,
          error
        );
      }
    }

    return results;
  }

  private async scanTwitter(brand: Brand): Promise<SocialMediaMonitor[]> {
    const results: SocialMediaMonitor[] = [];

    for (const keyword of brand.keywords) {
      try {
        // Search tweets
        const tweets = await this.twitterGateway.searchTweets(keyword, {
          maxResults: 50,
        });
        for (const tweet of tweets) {
          const monitor = await this.analyzeTwitterPost(tweet, brand, keyword);
          if (monitor) {
            results.push(monitor);
          }
        }

        // Search users
        const users = await this.twitterGateway.searchUsers(keyword);
        for (const user of users) {
          const monitor = await this.analyzeTwitterProfile(
            user,
            brand,
            keyword
          );
          if (monitor) {
            results.push(monitor);
          }
        }
      } catch (error) {
        console.error(`Error scanning Twitter for keyword ${keyword}:`, error);
      }
    }

    return results;
  }

  private async scanYouTube(brand: Brand): Promise<SocialMediaMonitor[]> {
    const results: SocialMediaMonitor[] = [];

    for (const keyword of brand.keywords) {
      try {
        const videos = await this.googleGateway.searchYouTube(keyword);
        for (const video of videos) {
          const monitor = await this.analyzeYouTubeVideo(video, brand, keyword);
          if (monitor) {
            results.push(monitor);
          }
        }
      } catch (error) {
        console.error(`Error scanning YouTube for keyword ${keyword}:`, error);
      }
    }

    return results;
  }

  private async analyzeFacebookPage(
    page: any,
    brand: Brand,
    keyword: string
  ): Promise<SocialMediaMonitor | null> {
    const similarityScore = this.calculateSimilarity(page.name, brand.name);

    if (similarityScore < 0.6) return null;

    const riskFactors = this.assessSocialMediaRisk(
      page,
      brand,
      "facebook",
      "page"
    );

    if (riskFactors.length === 0) return null;

    return this.socialMediaMonitorRepository.create({
      platform: "facebook",
      contentUrl: `https://facebook.com/${page.id}`,
      contentType: "page",
      content: page.about || "",
      authorUsername: page.username,
      authorDisplayName: page.name,
      authorProfile: {
        followers: page.fan_count || 0,
        following: 0,
        verified: page.verification_status === "blue_verified",
        profileImage: page.picture?.data?.url || "",
        bio: page.about || "",
        location: "",
        joinDate: new Date(),
      },
      status: "suspicious",
      similarityScore,
      riskFactors,
      detectionType: this.determineDetectionType(riskFactors),
      firstDetected: new Date(),
      lastChecked: new Date(),
      brandId: brand.id,
      isActive: true,
    });
  }

  private async analyzeFacebookPost(
    post: any,
    brand: Brand,
    keyword: string
  ): Promise<SocialMediaMonitor | null> {
    const content = post.message || "";
    const containsBrandKeywords = brand.keywords.some((k) =>
      content.toLowerCase().includes(k.toLowerCase())
    );

    if (!containsBrandKeywords) return null;

    const riskFactors = this.assessSocialMediaRisk(
      post,
      brand,
      "facebook",
      "post"
    );

    if (riskFactors.length === 0) return null;

    return this.socialMediaMonitorRepository.create({
      platform: "facebook",
      contentUrl: `https://facebook.com/${post.id}`,
      contentType: "post",
      content: content,
      authorUsername: post.from?.id,
      authorDisplayName: post.from?.name,
      engagement: {
        likes: post.likes?.summary?.total_count || 0,
        shares: post.shares?.count || 0,
        comments: post.comments?.summary?.total_count || 0,
        views: 0,
        reactions: {},
      },
      status: "suspicious",
      similarityScore: this.calculateContentSimilarity(content, brand),
      riskFactors,
      detectionType: this.determineDetectionType(riskFactors),
      publishedAt: new Date(post.created_time),
      firstDetected: new Date(),
      lastChecked: new Date(),
      brandId: brand.id,
      isActive: true,
    });
  }

  private async analyzeInstagramPost(
    post: any,
    brand: Brand,
    keyword: string
  ): Promise<SocialMediaMonitor | null> {
    const content = post.caption || "";
    const containsBrandKeywords = brand.keywords.some((k) =>
      content.toLowerCase().includes(k.toLowerCase())
    );

    if (!containsBrandKeywords) return null;

    const riskFactors = this.assessSocialMediaRisk(
      post,
      brand,
      "instagram",
      "post"
    );

    return this.socialMediaMonitorRepository.create({
      platform: "instagram",
      contentUrl: post.permalink,
      contentType: post.media_type === "VIDEO" ? "video" : "post",
      content: content,
      authorUsername: post.username,
      mediaUrls: [post.media_url],
      status: "suspicious",
      similarityScore: this.calculateContentSimilarity(content, brand),
      riskFactors,
      detectionType: this.determineDetectionType(riskFactors),
      publishedAt: new Date(post.timestamp),
      firstDetected: new Date(),
      lastChecked: new Date(),
      brandId: brand.id,
      isActive: true,
    });
  }

  private async analyzeTwitterPost(
    tweet: any,
    brand: Brand,
    keyword: string
  ): Promise<SocialMediaMonitor | null> {
    const content = tweet.text || "";
    const riskFactors = this.assessSocialMediaRisk(
      tweet,
      brand,
      "twitter",
      "post"
    );

    if (riskFactors.length === 0) return null;

    return this.socialMediaMonitorRepository.create({
      platform: "twitter",
      contentUrl: `https://twitter.com/i/status/${tweet.id}`,
      contentType: "post",
      content: content,
      authorUsername: tweet.author_id,
      engagement: {
        likes: tweet.public_metrics?.like_count || 0,
        shares: tweet.public_metrics?.retweet_count || 0,
        comments: tweet.public_metrics?.reply_count || 0,
        views: tweet.public_metrics?.impression_count || 0,
        reactions: {},
      },
      hashtags: tweet.entities?.hashtags?.map((h: any) => h.tag) || [],
      mentions: tweet.entities?.mentions?.map((m: any) => m.username) || [],
      status: "suspicious",
      similarityScore: this.calculateContentSimilarity(content, brand),
      riskFactors,
      detectionType: this.determineDetectionType(riskFactors),
      publishedAt: new Date(tweet.created_at),
      firstDetected: new Date(),
      lastChecked: new Date(),
      brandId: brand.id,
      isActive: true,
    });
  }

  private async analyzeTwitterProfile(
    user: any,
    brand: Brand,
    keyword: string
  ): Promise<SocialMediaMonitor | null> {
    const similarityScore = this.calculateSimilarity(user.name, brand.name);

    if (similarityScore < 0.7) return null;

    const riskFactors = this.assessSocialMediaRisk(
      user,
      brand,
      "twitter",
      "profile"
    );

    return this.socialMediaMonitorRepository.create({
      platform: "twitter",
      contentUrl: `https://twitter.com/${user.username}`,
      contentType: "profile",
      content: user.description || "",
      authorUsername: user.username,
      authorDisplayName: user.name,
      authorProfile: {
        followers: user.public_metrics?.followers_count || 0,
        following: user.public_metrics?.following_count || 0,
        verified: user.verified || false,
        profileImage: user.profile_image_url || "",
        bio: user.description || "",
        location: user.location || "",
        joinDate: new Date(),
      },
      status: "suspicious",
      similarityScore,
      riskFactors,
      detectionType: this.determineDetectionType(riskFactors),
      firstDetected: new Date(),
      lastChecked: new Date(),
      brandId: brand.id,
      isActive: true,
    });
  }

  private async analyzeYouTubeVideo(
    video: any,
    brand: Brand,
    keyword: string
  ): Promise<SocialMediaMonitor | null> {
    const content = `${video.snippet.title} ${video.snippet.description}`;
    const riskFactors = this.assessSocialMediaRisk(
      video,
      brand,
      "youtube",
      "video"
    );

    if (riskFactors.length === 0) return null;

    return this.socialMediaMonitorRepository.create({
      platform: "youtube",
      contentUrl: `https://youtube.com/watch?v=${video.id.videoId}`,
      contentType: "video",
      content: content,
      authorUsername: video.snippet.channelId,
      authorDisplayName: video.snippet.channelTitle,
      mediaUrls: [video.snippet.thumbnails?.high?.url],
      status: "suspicious",
      similarityScore: this.calculateContentSimilarity(content, brand),
      riskFactors,
      detectionType: this.determineDetectionType(riskFactors),
      publishedAt: new Date(video.snippet.publishedAt),
      firstDetected: new Date(),
      lastChecked: new Date(),
      brandId: brand.id,
      isActive: true,
    });
  }

  private calculateSimilarity(text1: string, text2: string): number {
    return similarity(text1.toLowerCase(), text2.toLowerCase());
  }

  private calculateContentSimilarity(content: string, brand: Brand): number {
    let maxSimilarity = 0;

    for (const keyword of brand.keywords) {
      const sim = this.calculateSimilarity(content, keyword);
      maxSimilarity = Math.max(maxSimilarity, sim);
    }

    return maxSimilarity;
  }

  private assessSocialMediaRisk(
    item: any,
    brand: Brand,
    platform: string,
    contentType: string
  ): any[] {
    const riskFactors = [];

    // Check for brand keywords in content
    const content = this.extractContent(item, contentType);
    if (content && brand.keywords) {
      const matchingKeywords = brand.keywords.filter((keyword) =>
        content.toLowerCase().includes(keyword.toLowerCase())
      );

      if (matchingKeywords.length > 0) {
        riskFactors.push({
          factor: "Brand keywords in content",
          severity: "medium",
          description: `Found brand keywords: ${matchingKeywords.join(", ")}`,
        });
      }
    }

    // Check for impersonation indicators
    if (contentType === "profile" || contentType === "page") {
      const name = item.name || item.displayName || "";
      const similarity = this.calculateSimilarity(name, brand.name);

      if (similarity > 0.8) {
        riskFactors.push({
          factor: "High name similarity",
          severity: "high",
          description: `Profile name "${name}" is very similar to brand name`,
        });
      }
    }

    // Check for suspicious engagement patterns
    if (item.public_metrics || item.engagement) {
      const metrics = item.public_metrics || item.engagement;
      const followers = metrics.followers_count || metrics.followers || 0;
      const engagement =
        (metrics.like_count || metrics.likes || 0) +
        (metrics.retweet_count || metrics.shares || 0);

      if (engagement > followers * 2) {
        riskFactors.push({
          factor: "Suspicious engagement ratio",
          severity: "medium",
          description: "Engagement significantly higher than follower count",
        });
      }
    }

    return riskFactors;
  }

  private extractContent(item: any, contentType: string): string {
    switch (contentType) {
      case "post":
        return item.message || item.text || item.caption || "";
      case "profile":
      case "page":
        return item.about || item.description || item.bio || "";
      case "video":
        return `${item.snippet?.title || ""} ${
          item.snippet?.description || ""
        }`;
      default:
        return "";
    }
  }

  private determineDetectionType(
    riskFactors: any[]
  ): SocialMediaMonitor["detectionType"] {
    const factors = riskFactors.map((f) => f.factor.toLowerCase());

    if (factors.some((f) => f.includes("name similarity"))) {
      return "impersonation";
    }

    if (factors.some((f) => f.includes("brand keywords"))) {
      return "trademark_infringement";
    }

    if (factors.some((f) => f.includes("suspicious"))) {
      return "scam";
    }

    return "impersonation";
  }

  @Mock("SocialMediaMonitoringService-updateSocialMediaStatus")
  async updateSocialMediaStatus(
    id: string,
    status: SocialMediaMonitor["status"],
    notes?: string
  ): Promise<SocialMediaMonitor | null> {
    const updateData: Partial<SocialMediaMonitor> = {
      status,
      lastChecked: new Date(),
    };
    if (notes) {
      updateData.notes = notes;
    }
    return this.socialMediaMonitorRepository.update(id, updateData);
  }

  @Mock("SocialMediaMonitoringService-getSocialMediaMonitors")
  async getSocialMediaMonitors(brandId: string): Promise<SocialMediaMonitor[]> {
    return this.socialMediaMonitorRepository.findByBrand(brandId);
  }

  @Mock("SocialMediaMonitoringService-getSocialMediaStats")
  async getSocialMediaStats(brandId: string): Promise<any> {
    return this.socialMediaMonitorRepository.getStatsByBrand(brandId);
  }
}
