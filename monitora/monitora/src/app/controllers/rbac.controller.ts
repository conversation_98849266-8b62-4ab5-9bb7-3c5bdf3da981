import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
} from "@core/decorators/decorators";
import { Request, Response } from "express";
import { RBACService } from "../services/rbac.service";
import { UserRepository } from "../repositories/user.repository";
import { RoleRepository } from "../repositories/role.repository";
import { PermissionRepository } from "../repositories/permission.repository";

@Controller("/api/rbac")
export class RBACController {
  constructor(
    private rbacService: RBACService,
    private userRepository: UserRepository,
    private roleRepository: RoleRepository,
    private permissionRepository: PermissionRepository
  ) {}

  // User Management Endpoints
  @Get("/users")
  async getUsers(req: Request, res: Response) {
    try {
      // Check permission - only system admins and admins can view users
      const userId = req.currentUser?.id;
      if (
        !userId ||
        !(await this.rbacService.checkPermission(userId, "users", "read"))
      ) {
        return res.status(403).json({ error: "Insufficient permissions" });
      }

      const users = await this.userRepository.findActiveUsers();

      res.json({
        success: true,
        data: users.map((user) => ({
          id: user.id,
          username: user.username,
          email: user.email,
          name: user.name,
          status: user.status,
          defaultRole: user.defaultRole,
          roles:
            user.roles?.map((role) => ({
              id: role.id,
              name: role.name,
              description: role.description,
            })) || [],
          createdAt: user.createdAt,
        })),
      });
    } catch (error) {
      console.error("Error getting users:", error);
      res.status(500).json({
        success: false,
        error: "Failed to get users",
      });
    }
  }

  @Post("/users/:userId/roles/:roleId")
  async assignRoleToUser(req: Request, res: Response) {
    try {
      // Check permission - only system admins can assign roles
      const currentUserId = req.currentUser?.id;
      if (
        !currentUserId ||
        !(await this.rbacService.checkPermission(
          currentUserId,
          "users",
          "manage"
        ))
      ) {
        return res.status(403).json({ error: "Insufficient permissions" });
      }

      const { userId, roleId } = req.params;
      const success = await this.rbacService.assignRoleToUser(userId, roleId);

      if (success) {
        res.json({
          success: true,
          message: "Role assigned successfully",
        });
      } else {
        res.status(400).json({
          success: false,
          error: "Failed to assign role",
        });
      }
    } catch (error) {
      console.error("Error assigning role:", error);
      res.status(500).json({
        success: false,
        error: "Failed to assign role",
      });
    }
  }

  @Delete("/users/:userId/roles/:roleId")
  async removeRoleFromUser(req: Request, res: Response) {
    try {
      // Check permission - only system admins can remove roles
      const currentUserId = req.currentUser?.id;
      if (
        !currentUserId ||
        !(await this.rbacService.checkPermission(
          currentUserId,
          "users",
          "manage"
        ))
      ) {
        return res.status(403).json({ error: "Insufficient permissions" });
      }

      const { userId, roleId } = req.params;
      const success = await this.rbacService.removeRoleFromUser(userId, roleId);

      if (success) {
        res.json({
          success: true,
          message: "Role removed successfully",
        });
      } else {
        res.status(400).json({
          success: false,
          error: "Failed to remove role",
        });
      }
    } catch (error) {
      console.error("Error removing role:", error);
      res.status(500).json({
        success: false,
        error: "Failed to remove role",
      });
    }
  }

  @Get("/users/:userId/permissions")
  async getUserPermissions(req: Request, res: Response) {
    try {
      const currentUserId = req.user?.id;
      const { userId } = req.params;

      // Users can view their own permissions, admins can view any user's permissions
      if (
        currentUserId !== userId &&
        !(await this.rbacService.checkPermission(
          currentUserId!,
          "users",
          "read"
        ))
      ) {
        return res.status(403).json({ error: "Insufficient permissions" });
      }

      const permissions = await this.rbacService.getUserPermissions(userId);

      res.json({
        success: true,
        data: {
          userId,
          permissions,
        },
      });
    } catch (error) {
      console.error("Error getting user permissions:", error);
      res.status(500).json({
        success: false,
        error: "Failed to get user permissions",
      });
    }
  }

  // Role Management Endpoints
  @Get("/roles")
  async getRoles(req: Request, res: Response) {
    try {
      // Check permission
      const userId = req.user?.id;
      if (
        !userId ||
        !(await this.rbacService.checkPermission(userId, "users", "read"))
      ) {
        return res.status(403).json({ error: "Insufficient permissions" });
      }

      const roles = await this.roleRepository.findAllActive();

      res.json({
        success: true,
        data: roles.map((role) => ({
          id: role.id,
          name: role.name,
          description: role.description,
          isSystemRole: role.isSystemRole,
          permissionCount: role.permissions?.length || 0,
          permissions:
            role.permissions?.map((p) => ({
              id: p.id,
              name: p.name,
              resource: p.resource,
              action: p.action,
            })) || [],
        })),
      });
    } catch (error) {
      console.error("Error getting roles:", error);
      res.status(500).json({
        success: false,
        error: "Failed to get roles",
      });
    }
  }

  @Post("/roles")
  async createRole(req: Request, res: Response) {
    try {
      // Check permission - only system admins can create roles
      const userId = req.user?.id;
      if (
        !userId ||
        !(await this.rbacService.checkPermission(userId, "users", "manage"))
      ) {
        return res.status(403).json({ error: "Insufficient permissions" });
      }

      const { name, description, permissionIds } = req.body;

      if (!name) {
        return res.status(400).json({
          success: false,
          error: "Role name is required",
        });
      }

      // Check if role already exists
      const existingRole = await this.roleRepository.findByName(name);
      if (existingRole) {
        return res.status(400).json({
          success: false,
          error: "Role with this name already exists",
        });
      }

      const role = await this.roleRepository.create({
        name,
        description,
        isActive: true,
        isSystemRole: false,
      });

      // Assign permissions if provided
      if (permissionIds && permissionIds.length > 0) {
        // This would require additional logic to assign permissions to the role
        // For now, we'll just create the role
      }

      res.status(201).json({
        success: true,
        data: role,
        message: "Role created successfully",
      });
    } catch (error) {
      console.error("Error creating role:", error);
      res.status(500).json({
        success: false,
        error: "Failed to create role",
      });
    }
  }

  // Permission Management Endpoints
  @Get("/permissions")
  async getPermissions(req: Request, res: Response) {
    try {
      // Check permission
      const userId = req.user?.id;
      if (
        !userId ||
        !(await this.rbacService.checkPermission(userId, "users", "read"))
      ) {
        return res.status(403).json({ error: "Insufficient permissions" });
      }

      const permissions = await this.permissionRepository.findAll();

      // Group permissions by resource
      const groupedPermissions = permissions.reduce((acc, permission) => {
        if (!acc[permission.resource]) {
          acc[permission.resource] = [];
        }
        acc[permission.resource].push({
          id: permission.id,
          name: permission.name,
          action: permission.action,
          description: permission.description,
          isSystemPermission: permission.isSystemPermission,
        });
        return acc;
      }, {} as any);

      res.json({
        success: true,
        data: groupedPermissions,
      });
    } catch (error) {
      console.error("Error getting permissions:", error);
      res.status(500).json({
        success: false,
        error: "Failed to get permissions",
      });
    }
  }

  @Get("/check-permission/:resource/:action")
  async checkPermission(req: Request, res: Response) {
    try {
      const userId = req.user?.id;
      const { resource, action } = req.params;

      if (!userId) {
        return res.status(401).json({ error: "Not authenticated" });
      }

      const hasPermission = await this.rbacService.checkPermission(
        userId,
        resource,
        action
      );

      res.json({
        success: true,
        data: {
          userId,
          resource,
          action,
          hasPermission,
        },
      });
    } catch (error) {
      console.error("Error checking permission:", error);
      res.status(500).json({
        success: false,
        error: "Failed to check permission",
      });
    }
  }

  @Post("/initialize")
  async initializeRBAC(req: Request, res: Response) {
    try {
      // Check permission - only system admins can initialize RBAC
      const userId = req.user?.id;
      if (
        !userId ||
        !(await this.rbacService.checkPermission(userId, "system", "manage"))
      ) {
        return res.status(403).json({ error: "Insufficient permissions" });
      }

      await this.rbacService.initializeDefaultRoles();

      res.json({
        success: true,
        message: "RBAC system initialized successfully",
      });
    } catch (error) {
      console.error("Error initializing RBAC:", error);
      res.status(500).json({
        success: false,
        error: "Failed to initialize RBAC",
      });
    }
  }
}
